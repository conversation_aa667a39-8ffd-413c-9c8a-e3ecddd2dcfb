class Member:
    def __init__(self, id=None, name=None, phone=None, email=None, join_date=None, status="active"):
        self.id = id
        self.name = name
        self.phone = phone
        self.email = email
        self.join_date = join_date
        self.status = status
    
    @staticmethod
    def get_all(db):
        cursor = db.cursor()
        cursor.execute("SELECT * FROM members ORDER BY name")
        return cursor.fetchall()
    
    @staticmethod
    def get_by_id(db, id):
        cursor = db.cursor()
        cursor.execute("SELECT * FROM members WHERE id = ?", (id,))
        return cursor.fetchone()
    
    def save(self, db):
        cursor = db.cursor()
        # إضافة أو تحديث العضو
        # ...