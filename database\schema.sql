CREATE TABLE members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    join_date DATE NOT NULL,
    status TEXT DEFAULT 'active'
);

CREATE TABLE income (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    member_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    date DATE NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    FOREIGN KEY (member_id) REFERENCES members(id)
);

CREATE TABLE expenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    amount DECIMAL(10,2) NOT NULL,
    date DATE NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    paid_by TEXT
);