import requests
import json
from config import WHATSAPP_API_KEY

class WhatsAppService:
    def __init__(self):
        self.api_key = WHATSAPP_API_KEY
        self.base_url = "https://api.whatsapp.com/v1/messages"
    
    def send_payment_notification(self, member, payment):
        """
        إرسال إشعار دفع للعضو عبر واتساب
        """
        message = f"مرحباً {member['name']}،\n"
        message += f"تم استلام دفعة بقيمة {payment['amount']} ريال بتاريخ {payment['date']}.\n"
        message += f"نوع الدفعة: {payment['type']}\n"
        message += "شكراً لدعمكم المستمر لديوان العشيرة."
        
        return self._send_message(member['phone'], message)
    
    def _send_message(self, phone, message):
        # تنفيذ الاتصال بواجهة برمجة واتساب
        # ...