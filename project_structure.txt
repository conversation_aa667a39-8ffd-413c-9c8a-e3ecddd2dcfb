tribe_diwan_system/
├── backend/
│   ├── app.py
│   ├── config.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── member.py
│   │   ├── income.py
│   │   ├── expense.py
│   ├── controllers/
│   │   ├── __init__.py
│   │   ├── auth_controller.py
│   │   ├── member_controller.py
│   │   ├── finance_controller.py
│   │   ├── report_controller.py
│   │   ├── whatsapp_controller.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── whatsapp_service.py
│   │   ├── report_service.py
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── App.js
│   │   ├── index.js
├── database/
│   ├── schema.sql