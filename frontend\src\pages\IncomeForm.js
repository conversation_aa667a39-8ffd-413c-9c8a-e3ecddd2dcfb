import React, { useState, useEffect } from 'react';
import { Form, Button, Select, DatePicker, Input, message } from 'antd';
import axios from 'axios';

const IncomeForm = () => {
  const [members, setMembers] = useState([]);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    // جلب قائمة الأعضاء
    axios.get('/api/members')
      .then(res => setMembers(res.data))
      .catch(err => message.error('فشل في تحميل بيانات الأعضاء'));
  }, []);
  
  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/income', {
        ...values,
        date: values.date.format('YYYY-MM-DD')
      });
      
      message.success('تم تسجيل الإيراد بنجاح');
      form.resetFields();
      
      // إرسال إشعار واتساب إذا تم اختيار ذلك
      if (values.sendNotification) {
        await axios.post('/api/whatsapp/payment-notification', {
          memberId: values.memberId,
          paymentId: response.data.id
        });
        message.success('تم إرسال إشعار الدفع عبر واتساب');
      }
    } catch (error) {
      message.error('حدث خطأ أثناء تسجيل الإيراد');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="income-form-container">
      <h2>تسجيل إيراد جديد</h2>
      <Form form={form} onFinish={handleSubmit} layout="vertical">
        {/* حقول النموذج */}
      </Form>
    </div>
  );
};

export default IncomeForm;